        Qe and r        Qnformation where it adds value, such as requirements, compliance steps, and expected timelines.\n        Country-Specific Focus \u2013 Each section should reflect local regulations and business norms rather than generic global advice.\n        No Preamble \u2013 Only output the content that has been requested. do not provide an intro or outro preamble. \n        Neutral & Informative \u2013 Do not give subjective opinions or suggest external consultation; instead, focus on clear, actionable regulatory insights.\n        \n        When generating content for each section, select the most appropriate format based on the nature of the information:\n        Use paragraphs for sections that require narrative explanations, background details, or descriptive overviews. These should be written in clear, structured, and professional language to ensure readability while maintaining an expert tone.\n        Example: \"The Australian economy has a strong service sector, with financial and insurance services being key drivers. Over the past 15 years, GDP growth has averaged 3.6% annually, with ongoing investments in renewable energy and technology.\"\n        Use bullet points when presenting lists of key facts, advantages, or challenges where concise information improves readability.\n        Example:\n        Ease of Business Formation: Registering a private company requires only A$1 in capital.\n        Economic Stability: Australia is politically stable with a low inflation rate.\n        Investment Restrictions: Foreign ownership is restricted in strategic sectors such as defense and media.\n        Use tables when comparing entity types, tax rates, compliance requirements, or market entry options. This ensures structured, side-by-side comparisons for quick reference.\n\n        We can not store a table as part of the output though so for any table formatted content (such as entity types, tax rates, or incorporation timelines), format it as a structured string where:\n        Columns are separated by | (pipe).\n        Rows are separated by ; (semicolon).\n        Example: \"Field | Requirement; Minimum Capital | None; Corporate Tax | 30%\"\n\n        Use a combination of formats when the section contains mixed content. Introduce key points with a paragraph, summarize lists with bullet points, and use tables for structured comparisons.\n        Example:\n        Business Incorporation Overview: The process of setting up a business in Australia varies based on the entity type. The most common options include LLCs, PLCs, and Branch Offices.\n        Steps to Incorporate:\n        Reserve the company name with the Australian Business Register.\n        Register with the Australian Securities and Investment Commission (ASIC).\n        Obtain a Tax Filing Number and Australian Business Number (ABN).\n\n        Based on the input text_content, you are requested to provide the URL citations for each line of text you generate. Provide the entire URL inline with the generated text. \n        URL citations are a must have for the generated output, you will be heavily penalized for not generating the citations.\n        Don't just mention [^1^] as the citation or mention URL1, provide the entire URL as the citation. There is a huge penalty for not following this instruction. \n\n        Please also ensure to generate content strictly for point provided above. \n        Please ignore any context that is not in English. Also, generate the output only in Human Readable English Language.\n        "}], "system": "You specialise in generating structured and digestible regulatory guides for businesses looking to expand their business and move employees globally. Focus only on the topic provided. Do not include broader context, background, or related topics. ", "max_tokens": 20000, "temperature": 0} ooking to expand their business and move employees globally. Focus only on the topic provided. Do not include broader context, back