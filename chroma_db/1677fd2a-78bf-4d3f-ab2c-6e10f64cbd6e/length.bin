        /nclude_        / "include_distances": 1, "$process_person_profile": false, "chroma_version": "0.6.3", "server_context": "None", "hosted": false, "chroma_api_impl": "chromadb.api.segment.SegmentAPI", "is_persistent": false, "chroma_server_ssl_enabled": false, "chroma_server_api_default_path": "/api/v2", "$python_runtime": "CPython", "$python_version": "3.11.0", "$os": "Mac OS X", "$os_version": "15.2", "$lib": "posthog-python", "$lib_version": "3.23.0", "$geoip_disable": true}, "timestamp": "2025-04-27T18:26:38.389739+00:00", "distinct_id": "8d65bf49-6964-4421-bcfc-c5abca0bd95a", "event": "CollectionQueryEvent"}, {"properties": {"batch_size": 1, "collection_uuid": "cc6a7f6c-8220-4782-bb3b-13239a37d9bb", "ids_count": 3, "limit": 0, "include_metadata": 3, "include_documents": 3, "include_uris": 0, "$process_person_profile": false, "chroma_version": "0.6.3", "server_context": "None", "hosted": false, "chroma_api_impl": "chromadb.api.segment.SegmentAPI", "is_persistent": false, "chroma_server_ssl_enabled": false, "chroma_server_api_default_path": "/api/v2", "$python_runtime": "CPython", "$python_version": "3.11.0", "$os": "Mac OS X", "$os_version": "15.2", "$lib": "posthog-python", "$lib_version": "3.23.0", "$geoip_disable": true}, "timestamp": "2025-04-27T18:26:38.391870+00:00", "distinct_id": "8d65bf49-6964-4421-bcfc-c5abca0bd95a", "event": "CollectionGetEvent"}, {"properties": {"batch_size": 1, "collection_uuid": "849f290c-219f-48a3-8171-42e7c85915c8", "add_amount": 1, "with_documents": 1, "with_metadata": 0, "with_uris": 0, "$process_person_profile": false, "chroma_version": "0.6.3", "server_context": "None", "hosted": false, "chroma_api_impl": "chromadb.api.segment.SegmentAPI", "is_persistent": false, "chroma_server_ssl_enabled": false, "chroma_server_api_default_path": "/api/v2", "$python_runtime": "CPython", "$python_version": "3.11.0", "$os": "Mac OS X", "$os_version": "15.2", "$lib": "posthog-python", "$lib_version": "3.23.0", "$geoip_disable": true}, "timestamp": "2025-04-27T18:26:38.451282+00:00", "distinct_id": "8d65bf49-6964-4421-bcfc-c5abca0bd95a", "event": "CollectionAddEvent"}, {"properties": {"batch_size": 1, "collection_uuid": "9b1c4d1e-e184-4cb3-919d-6670701f06d4", "add_amount": 1, "with_documents": 1, "with_metadata": 0, "with_uris": 0, "$process_person_profile": false, "chroma_version": "0.6.3", "server_context": "None", "hosted": false, "chroma_api_impl": "chromadb.api.segment.SegmentAPI", "is_persistent": false, "chroma_server_ssl_enabled": false, "chroma_server_api_default_path": "/api/v2", "$python_runtime": "CPython", "$python_version": "3.11.0", "$os": "Mac OS X", "$os_version": "15.2", "$lib": "posthog-python", "$lib_version": "3.23.0", "$geoip_disable": true}, "timestamp": "2025-04-27T18:26:38.468539+00:00", "distinct_id": "8d65bf49-6964-4421-bcfc-c5abca0bd95a", "event": "CollectionAddEvent"}, {"properties": {"batch_size": 1, "collection_uuid": "849f290c-219f-48a3-8171-42e7c85915c8", "query_amount": 1, "with_metadata_filter": 0, "with_document_filter": 0, "n_results": 5, "include_metadatas": 1, "include_documents": 1, "include_uris": 0, "include_distances": 1, "$process_person_profile": false, "chroma_version": "0.6.3", "server_context": "None", "hosted": false, "chroma_api_impl": "chromadb.api.segment.SegmentAPI", "is_persistent": false, "chroma_server_ssl_enabled": false, "chroma_server_api_default_path": "/api/v2", "$python_runtime": "CPython", "$python_version": "3.11.0", "$os": "Mac OS X", "$os_version": "15.2", "$lib": "posthog-python", "$lib_version": "3.23.0", "$geoip_disable": true}, "timestamp": "2025-04-27T18:26:38.580464+00:00", "distinct_id": "8d65bf49-6964-4421-bcfc-c5abca0bd95a", "event": "CollectionQueryEvent"}, {"properties": {"batch_size": 1, "collection_uuid": "849f290c-219f-48a3-8171-42e7c85915c8", "ids_count": 3, "limit": 0, "include_metadata": 3, "include_documents": 3, "include_uris": 0, "$process_person_profile": false, "chroma_version": "0.6.3", "server_context": "None"