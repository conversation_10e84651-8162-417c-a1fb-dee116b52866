        5 v u k         5 a t "   c o n t e n t = " g u i d e " > 
         < m e t a   c h a r s e t = " u t f - 8 " > 
         < t i t l e   l a n g = " e n " > 
             S k i l l e d   W o r k e r   v i s a :   E x t e n d   y o u r   v i s a   -   G O V . U K 
     < / t i t l e > 
 
         < s c r i p t   s r c = " / a s s e t s / s t a t i c / g o v u k _ p u b l i s h i n g _ c o m p o n e n t s / v e n d o r / l u x / l u x - m e a s u r e r - 8 2 2 5 2 e 2 6 b a 9 3 3 f 8 4 1 1 4 f 5 8 9 0 8 8 b c 3 b 0 2 c 8 0 2 6 6 d 0 c 7 d e f 8 2 3 2 3 7 d f c 7 8 7 f a 0 8 7 9 7 . j s "   a s y n c = " a s y n c " > < / s c r i p t > 
         < s c r i p t   s r c = " / a s s e t s / s t a t i c / g o v u k _ p u b l i s h i n g _ c o m p o n e n t s / r u m - c u s t o m - d a t a - 9 c 1 4 c 4 b 4 4 3 5 d 3 b a f 5 5 5 5 7 c b 0 6 8 2 d 3 5 7 1 3 8 4 f f 8 5 8 9 0 e 5 2 0 2 5 9 5 7 4 d 2 4 6 d 9 b 9 9 8 7 6 . j s "   t y p e = " m o d u l e " > < / s c r i p t > 
         < s c r i p t   s r c = " / a s s e t s / s t a t i c / g o v u k _ p u b l i s h i n g _ c o m p o n e n t s / r u m - l o a d e r - a 6 5 b 1 0 e 1 8 c e e b a 3 b d 8 a 2 e a c 5 0 7 c 7 f 2 c 5 1 3 c d c 8 2 f 3 5 0 9 7 d f 9 0 3 f d e a 8 7 f 1 d c 2 e 3 3 . j s "   a s y n c = " a s y n c "   d a t a - l u x - r e p o r t e r - s c r i p t = " / a s s e t s / s t a t i c / g o v u k _ p u b l i s h i n g _ c o m p o n e n t s / v e n d o r / l u x / l u x - r e p o r t e r - a 2 b 7 2 b a 5 b 8 5 e 0 5 e 0 3 5 2 b b d 1 0 c d 8 1 e b a 9 5 2 a 7 5 3 b 3 5 8 f 1 1 b 4 5 e d b c b c 1 f 6 d b 2 1 c 7 e . j s " > < / s c r i p t > 
 
         < m e t a   n a m e = " g o v u k : c o m p o n e n t s _ g e m _ v e r s i o n "   c o n t e n t = " 5 6 . 2 . 2 " > 
         < s c r i p t   s r c = " / a s s e t s / s t a t i c / g o v u k _ p u b l i s h i n g _ c o m p o n e n t s / l o a d - a n a l y t i c s - 1 6 e c 3 9 9 a b b 3 0 f a 7 1 b 3 7 2 a 5 5 e c 2 7 c 5 e a 5 0 6 b 3 a 0 7 d c 4 3 d a 5 a 1 8 6 7 8 2 7 1 f 9 c 8 8 3 3 b d . j s "   t y p e = " m o d u l e " > < / s c r i p t > 
 
         
 
         < l i n k   r e l = " s t y l e s h e e t "   h r e f = " / a s s e t s / s t a t i c / a p p l i c a t i o n - 5 2 d 3 6 3 5 9 c 3 6 4 1 1 d 2 a a 4 8 c 2 4 6 9 7 b 6 0 2 3 2 c a 1 0 7 7 d 7 c a 7 f e f d c d b 8 d 4 e c b e 4 d 8 6 c f 7 . c s s "   m e d i a = " a l l " > 
         < l i n k   r e l = " i c o n "   s i z e s = " 4 8 x 4 8 "   h r e f = " / a s s e t s / s t a t i c / f a v i c o n - f 5 4 8 1 6 f c 1 5 9 9 7 b d 4 2 c d 9 0 e 4 c 5 0 b 8 9 6 a 1 f c 0 9 8 c 0 c 3 2 9 5 7 d 4 e 5 e f f b f a 9 f 9 b 3 5 e 5 3 . i c o " > 
         < l i n k   r e l = " i c o n "   s i z e s = " a n y "   h r e f = " / a s s e t s / s t a t i c / f a v i c o n - 5 0 1 4 4 c 9 d 8 3 e 5 9 5 8 4 c 4 5 b 2 4 9 a d 9 e 9 a b f d d 2 3 6 8 9 8 7 6 c 3 3 f 2 8 4 5 7 d f 1 3 b b d d 9 c 8 6 8 8 . s v g "   t y p e = " i m a g e / s v g + x m l " > 
         < l i n k   r e l = " m a s k - i c o n "   h r e f = " / a s s e t s / s t a t i c / g o v u k - i c o n - m a s k - c d f 4 2 6 5 1 6 5 f 8 d 7 f 9 e e c 5 4 a a 2 c 1 d f b b 3 d 8 b 6 d 2 9 7 c 5 d 7 9 1 9 f 0 3 1 3 e 0 8 3 6 a 5 8 0 4 b b 6 . s v g "   c o l o r = " # 0 b 0 c 0 c " > 
         < l i n k   r e l = " a p p l e - t o u c h - i c o n "   h r e f = " / a s s e t s / s t a t i c / g o v u k - i c o n - 1 8 0 - d 2 d 7 3 9 9 f f 2 b a 0 5 3 7 2 b 6 b 2 0 1 8 c c 6 7 0 5 3 e 4 5 8 a 7 4 8 c c e e a 1 a 5 5 0 d 8 0 4 d b e c 4 0 1 e 3 e d . p n g " > 
 
         < m e t a   n a m e = " t h e m e - c o l o r "   c o n t e n t = " # 0 b 0 c 0 c " > 
         < m e t a   n a m e = " v i e w p o r t "   c o n t e n t = " w i d t h = d e v i c e - w i d t h ,   i n i t i a l - s c a l e = 1 " > 
 
         < m e t a   p r o p e r t y = " o g : i m a g e "   c o n t e n t = " h t t p s : / / w w w . g o v . u k / a s s e t s / s t a t i c / g o v u k - o p e n g r a p h - i m a g e - 0 