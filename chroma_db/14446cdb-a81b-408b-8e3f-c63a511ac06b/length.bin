        (-45b0-b        (4ab1880", "$process_person_profile": false, "chroma_version": "0.6.3", "server_context": "None", "hosted": false, "chroma_api_impl": "chromadb.api.segment.SegmentAPI", "is_persistent": false, "chroma_server_ssl_enabled": false, "chroma_server_api_default_path": "/api/v2", "$python_runtime": "CPython", "$python_version": "3.11.0", "$os": "Mac OS X", "$os_version": "15.2", "$lib": "posthog-python", "$lib_version": "3.23.0", "$geoip_disable": true}, "timestamp": "2025-04-29T06:05:04.654074+00:00", "distinct_id": "8d65bf49-6964-4421-bcfc-c5abca0bd95a", "event": "ClientCreateCollectionEvent"}, {"properties": {"batch_size": 1, "collection_uuid": "22fb0b81-85e3-495b-bf67-890726cde161", "add_amount": 1, "with_documents": 1, "with_metadata": 0, "with_uris": 0, "$process_person_profile": false, "chroma_version": "0.6.3", "server_context": "None", "hosted": false, "chroma_api_impl": "chromadb.api.segment.SegmentAPI", "is_persistent": false, "chroma_server_ssl_enabled": false, "chroma_server_api_default_path": "/api/v2", "$python_runtime": "CPython", "$python_version": "3.11.0", "$os": "Mac OS X", "$os_version": "15.2", "$lib": "posthog-python", "$lib_version": "3.23.0", "$geoip_disable": true}, "timestamp": "2025-04-29T06:05:04.810267+00:00", "distinct_id": "8d65bf49-6964-4421-bcfc-c5abca0bd95a", "event": "CollectionAddEvent"}, {"properties": {"batch_size": 1, "collection_uuid": "91cf3590-f372-45b0-b79b-66ccb4ab1880", "add_amount": 1, "with_documents": 1, "with_metadata": 0, "with_uris": 0, "$process_person_profile": false, "chroma_version": "0.6.3", "server_context": "None", "hosted": false, "chroma_api_impl": "chromadb.api.segment.SegmentAPI", "is_persistent": false, "chroma_server_ssl_enabled": false, "chroma_server_api_default_path": "/api/v2", "$python_runtime": "CPython", "$python_version": "3.11.0", "$os": "Mac OS X", "$os_version": "15.2", "$lib": "posthog-python", "$lib_version": "3.23.0", "$geoip_disable": true}, "timestamp": "2025-04-29T06:05:04.827031+00:00", "distinct_id": "8d65bf49-6964-4421-bcfc-c5abca0bd95a", "event": "CollectionAddEvent"}, {"properties": {"batch_size": 1, "collection_uuid": "22fb0b81-85e3-495b-bf67-890726cde161", "query_amount": 1, "with_metadata_filter": 0, "with_document_filter": 0, "n_results": 5, "include_metadatas": 1, "include_documents": 1, "include_uris": 0, "include_distances": 1, "$process_person_profile": false, "chroma_version": "0.6.3", "server_context": "None", "hosted": false, "chroma_api_impl": "chromadb.api.segment.SegmentAPI", "is_persistent": false, "chroma_server_ssl_enabled": false, "chroma_server_api_default_path": "/api/v2", "$python_runtime": "CPython", "$python_version": "3.11.0", "$os": "Mac OS X", "$os_version": "15.2", "$lib": "posthog-python", "$lib_version": "3.23.0", "$geoip_disable": true}, "timestamp": "2025-04-29T06:05:04.944466+00:00", "distinct_id": "8d65bf49-6964-4421-bcfc-c5abca0bd95a", "event": "CollectionQueryEvent"}, {"properties": {"batch_size": 1, "collection_uuid": "22fb0b81-85e3-495b-bf67-890726cde161", "ids_count": 1, "limit": 0, "include_metadata": 1, "include_documents": 1, "include_uris": 0, "$process_person_profile": false, "chroma_version": "0.6.3", "server_context": "None", "hosted": false, "chroma_api_impl": "chromadb.api.segment.SegmentAPI", "is_persistent": false, "chroma_server_ssl_enabled": false, "chroma_server_api_default_path": "/api/v2", "$python_runtime": "CPython", "$python_version": "3.11.0", "$os": "Mac OS X", "$os_version": "15.2", "$lib": "posthog-python", "$lib_version": "3.23.0", "$geoip_disable": true}, "timestamp": "2025-04-29T06:05:04.951676+00:00", "distinct_id": "8d65bf49-6964-4421-bcfc-c5abca0bd95a", "event": "CollectionGetEvent"}, {"properties": {"batch_size": 1, "collection_uuid": "91cf3590-f372-45b0-b79b-66ccb4ab1880", "query_amount": 1, "with_metadata_filter": 0, "with_document_filter": 0, "n_results": 5, "include_metadatas": 1, "include_documents": 1, "include_uris": 0, "include_distances": 1, "$proce